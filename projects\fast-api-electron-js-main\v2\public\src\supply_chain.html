<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>SC Copilot KI - Autonomous Supply Chain System</title>
  <script src="https://unpkg.com/@tabler/core@1.0.0-beta3/dist/js/tabler.min.js"></script>
  <link rel="stylesheet" href="../assets/css/tabler.min.css">
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .glass-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      transition: all 0.3s ease;
    }

    .glass-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
    }

    .glass-button {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      color: white;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px 0 rgba(31, 38, 135, 0.3);
    }

    .glass-button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px 0 rgba(31, 38, 135, 0.4);
      color: white;
    }

    .glass-button:active {
      transform: translateY(0);
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 8px;
    }

    .status-pending {
      background-color: #ffc107;
      animation: pulse 2s infinite;
    }

    .status-running {
      background-color: #17a2b8;
      animation: pulse 1s infinite;
    }

    .status-completed {
      background-color: #28a745;
    }

    .status-error {
      background-color: #dc3545;
    }

    @keyframes pulse {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
      100% {
        opacity: 1;
      }
    }

    .progress-glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(5px);
      border-radius: 10px;
      overflow: hidden;
    }

    .progress-bar-glass {
      background: linear-gradient(90deg, #00d4ff, #090979);
      transition: width 0.5s ease;
    }

    .card-title {
      color: white;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .card-text {
      color: rgba(255, 255, 255, 0.9);
    }

    .main-title {
      color: white;
      font-size: 2.5rem;
      font-weight: 800;
      text-align: center;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      margin-bottom: 2rem;
    }

    .operation-step {
      padding: 15px;
      margin: 10px 0;
      border-radius: 15px;
      background: rgba(255, 255, 255, 0.05);
      border-left: 4px solid #00d4ff;
    }

    .log-container {
      max-height: 300px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 10px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
    }

    .log-entry {
      color: #00ff00;
      margin: 5px 0;
    }

    .log-error {
      color: #ff6b6b;
    }

    .log-warning {
      color: #ffd93d;
    }

    .log-info {
      color: #74c0fc;
    }
  </style>
</head>

<body class="antialiased">
  <div class="container-xl py-4">
    <h1 class="main-title">SC Copilot KI - Autonomous Supply Chain System</h1>
    
    <div class="row g-4">
      <!-- Control Panel -->
      <div class="col-md-4">
        <div class="glass-card p-4">
          <h3 class="card-title mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
              <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
              <path d="M2 17l10 5 10-5"></path>
              <path d="M2 12l10 5 10-5"></path>
            </svg>
            Control Panel
          </h3>
          
          <div class="mb-4">
            <button id="startAutomation" class="glass-button btn w-100 mb-3" onclick="startAutomation()">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
                <polygon points="5,3 19,12 5,21 5,3"></polygon>
              </svg>
              Start AI Automation
            </button>
            
            <button class="glass-button btn w-100 mb-3" onclick="refreshStatus()">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
                <polyline points="23,4 23,10 17,10"></polyline>
                <path d="M20.49,15a9,9,0,1,1-2.12-9.36L23,10"></path>
              </svg>
              Refresh Status
            </button>
            
            <button class="glass-button btn w-100" onclick="viewResults()">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
                <path d="M14,2H6A2,2,0,0,0,4,4V20a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V8Z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
              View Results
            </button>
          </div>
          
          <div class="progress-glass mb-3">
            <div id="overallProgress" class="progress-bar-glass" style="width: 0%; height: 8px;"></div>
          </div>
          <small class="card-text">Overall Progress: <span id="progressText">0%</span></small>
        </div>
      </div>
      
      <!-- Operations Status -->
      <div class="col-md-8">
        <div class="glass-card p-4">
          <h3 class="card-title mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
              <path d="M9,12l2,2l4-4"></path>
              <path d="M21,12c0,4.97-4.03,9-9,9s-9-4.03-9-9s4.03-9,9-9c1.66,0,3.22,0.45,4.55,1.24"></path>
            </svg>
            SC Copilot KI - Autonomous Operation
          </h3>
          
          <div id="operationSteps">
            <div class="operation-step">
              <span id="step1Status" class="status-indicator status-pending"></span>
              <strong>Step 1:</strong> <span class="card-text">Detecting Critical Materials</span>
              <div id="step1Details" class="mt-2 small card-text"></div>
            </div>
            
            <div class="operation-step">
              <span id="step2Status" class="status-indicator status-pending"></span>
              <strong>Step 2:</strong> <span class="card-text">Composing & Sending Emails</span>
              <div id="step2Details" class="mt-2 small card-text"></div>
            </div>
            
            <div class="operation-step">
              <span id="step3Status" class="status-indicator status-pending"></span>
              <strong>Step 3:</strong> <span class="card-text">Fetching Emails from Outlook</span>
              <div id="step3Details" class="mt-2 small card-text"></div>
            </div>
            
            <div class="operation-step">
              <span id="step4Status" class="status-indicator status-pending"></span>
              <strong>Step 4:</strong> <span class="card-text">Processing Emails with AI</span>
              <div id="step4Details" class="mt-2 small card-text"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- System Logs -->
    <div class="row g-4 mt-2">
      <div class="col-12">
        <div class="glass-card p-4">
          <h3 class="card-title mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
              <path d="M14,2H6A2,2,0,0,0,4,4V20a2,2,0,0,0,2,2H18a2,2,0,0,0,2-2V8Z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
            </svg>
            System Logs
          </h3>
          <div id="systemLogs" class="log-container">
            <div class="log-entry log-info">[INFO] System initialized and ready</div>
            <div class="log-entry log-info">[INFO] Waiting for automation to start...</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Results Modal -->
    <div class="modal fade" id="resultsModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content glass-card">
          <div class="modal-header border-0">
            <h5 class="modal-title card-title">Automation Results</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div id="resultsContent" class="card-text">
              <!-- Results will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="../assets/js/supply_chain.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>