import pandas as pd
import json
import os
import win32com.client
import google.generativeai as genai
from datetime import datetime
import time
from typing import Dict, List, Any
import re

class SupplyChainAutomation:
    def __init__(self):
        self.data_path = "data/data.csv"
        self.email_info_path = "data/email_info.csv"
        self.critical_materials_path = "data/critical_materials.json"
        self.inbox_path = "data/inbox.json"
        self.sent_path = "data/sent.json"
        self.discrepancy_path = "data/discrepancy.json"
        self.supplier_response_path = "data/supplier_response.json"
        self.unloading_status_path = "data/unloading_status.json"
        
        # Initialize Gemini AI
        genai.configure(api_key=os.getenv('GEMINI_API_KEY', 'your-gemini-api-key-here'))
        self.model = genai.GenerativeModel('gemini-pro')
        
        # Initialize Outlook
        try:
            self.outlook = win32com.client.Dispatch("Outlook.Application")
            self.namespace = self.outlook.GetNamespace("MAPI")
        except Exception as e:
            print(f"Error initializing Outlook: {e}")
            self.outlook = None
    
    def detect_critical_materials(self) -> Dict[str, Any]:
        """Detect critical materials from data.csv and store in critical_materials.json"""
        try:
            df = pd.read_csv(self.data_path)
            critical_materials = []
            
            for _, row in df.iterrows():
                stock = row['Stock']
                status = "red" if stock < 100 else "yellow" if stock == 100 else "green"
                
                if status in ["red", "yellow"]:
                    critical_materials.append({
                        "index": row['Index'],
                        "material": row['Material'],
                        "material_text": row['Material short text'],
                        "stock": stock,
                        "status": status,
                        "detected_at": datetime.now().isoformat()
                    })
            
            # Save to JSON
            with open(self.critical_materials_path, 'w') as f:
                json.dump(critical_materials, f, indent=2)
            
            return {
                "success": True,
                "count": len(critical_materials),
                "materials": critical_materials
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def load_email_info(self) -> Dict[str, str]:
        """Load email information from CSV"""
        try:
            df = pd.read_csv(self.email_info_path)
            email_info = {}
            for _, row in df.iterrows():
                email_info[row['type']] = row['email_address']
            return email_info
        except Exception as e:
            print(f"Error loading email info: {e}")
            return {}
    
    def compose_and_send_emails(self) -> Dict[str, Any]:
        """Compose and send emails for each critical material"""
        try:
            if not self.outlook:
                return {"success": False, "error": "Outlook not available"}
            
            # Load critical materials
            with open(self.critical_materials_path, 'r') as f:
                critical_materials = json.load(f)
            
            email_info = self.load_email_info()
            sent_emails = []
            
            for material in critical_materials:
                # Send to warehouse
                warehouse_email = self.send_warehouse_email(material, email_info.get('warehouse'))
                if warehouse_email:
                    sent_emails.append(warehouse_email)
                
                # Send to supplier
                supplier_email = self.send_supplier_email(material, email_info.get('supplier'))
                if supplier_email:
                    sent_emails.append(supplier_email)
                
                # Send to logistics
                logistics_email = self.send_logistics_email(material, email_info.get('logistics'))
                if logistics_email:
                    sent_emails.append(logistics_email)
            
            # Save sent emails
            with open(self.sent_path, 'w') as f:
                json.dump(sent_emails, f, indent=2)
            
            return {
                "success": True,
                "sent_count": len(sent_emails),
                "emails": sent_emails
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def send_warehouse_email(self, material: Dict, email: str) -> Dict:
        """Send email to warehouse for physical inventory check"""
        try:
            mail = self.outlook.CreateItem(0)  # 0 = olMailItem
            mail.To = email
            mail.Subject = f"URGENT: Physical Inventory Check Required - {material['material']}"
            mail.Body = f"""
Dear Warehouse Team,

We have detected a critical stock situation for the following material:

Material Code: {material['material']}
Description: {material['material_text']}
Current System Stock: {material['stock']} units
Status: {material['status'].upper()}

Please conduct an immediate physical inventory check and report the actual quantity available.

This is an automated message from the Supply Chain AI System.

Best regards,
Supply Chain Automation System
            """
            mail.Send()
            
            return {
                "type": "warehouse",
                "recipient": email,
                "material": material['material'],
                "subject": mail.Subject,
                "sent_at": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"Error sending warehouse email: {e}")
            return None
    
    def send_supplier_email(self, material: Dict, email: str) -> Dict:
        """Send email to supplier for delivery updates"""
        try:
            mail = self.outlook.CreateItem(0)
            mail.To = email
            mail.Subject = f"URGENT: Delivery Status Update Required - {material['material']}"
            mail.Body = f"""
Dear Supplier,

We urgently need a delivery status update for the following material:

Material Code: {material['material']}
Description: {material['material_text']}
Current Stock Level: {material['stock']} units
Status: {material['status'].upper()}

Please provide:
1. Expected delivery date
2. Quantity to be delivered
3. Any potential delays or issues

This is an automated message from the Supply Chain AI System.

Best regards,
Supply Chain Automation System
            """
            mail.Send()
            
            return {
                "type": "supplier",
                "recipient": email,
                "material": material['material'],
                "subject": mail.Subject,
                "sent_at": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"Error sending supplier email: {e}")
            return None
    
    def send_logistics_email(self, material: Dict, email: str) -> Dict:
        """Send email to logistics for unloading status"""
        try:
            mail = self.outlook.CreateItem(0)
            mail.To = email
            mail.Subject = f"URGENT: Unloading Status Check - {material['material']}"
            mail.Body = f"""
Dear Logistics Team,

We need an immediate status update on unloading operations for:

Material Code: {material['material']}
Description: {material['material_text']}
Current Stock Level: {material['stock']} units
Status: {material['status'].upper()}

Please confirm:
1. Any pending unloading operations
2. Expected completion time
3. Quantity being unloaded

This is an automated message from the Supply Chain AI System.

Best regards,
Supply Chain Automation System
            """
            mail.Send()
            
            return {
                "type": "logistics",
                "recipient": email,
                "material": material['material'],
                "subject": mail.Subject,
                "sent_at": datetime.now().isoformat()
            }
        except Exception as e:
            print(f"Error sending logistics email: {e}")
            return None
    
    def fetch_emails_from_outlook(self) -> Dict[str, Any]:
        """Fetch emails from Outlook and store in inbox.json"""
        try:
            if not self.outlook:
                return {"success": False, "error": "Outlook not available"}
            
            inbox = self.namespace.GetDefaultFolder(6)  # 6 = olFolderInbox
            messages = inbox.Items
            messages.Sort("[ReceivedTime]", True)
            
            fetched_emails = []
            
            # Get recent emails (last 50)
            for i in range(min(50, messages.Count)):
                message = messages[i]
                try:
                    email_data = {
                        "subject": message.Subject,
                        "sender": message.SenderEmailAddress,
                        "body": message.Body,
                        "received_time": message.ReceivedTime.strftime("%Y-%m-%d %H:%M:%S"),
                        "category": self.categorize_email(message.Subject, message.Body)
                    }
                    fetched_emails.append(email_data)
                except Exception as e:
                    print(f"Error processing email {i}: {e}")
                    continue
            
            # Save to JSON
            with open(self.inbox_path, 'w') as f:
                json.dump(fetched_emails, f, indent=2)
            
            return {
                "success": True,
                "count": len(fetched_emails),
                "emails": fetched_emails
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def categorize_email(self, subject: str, body: str) -> str:
        """Categorize email based on content"""
        subject_lower = subject.lower()
        body_lower = body.lower()
        
        if any(word in subject_lower or word in body_lower for word in ['warehouse', 'inventory', 'physical', 'stock']):
            return 'warehouse'
        elif any(word in subject_lower or word in body_lower for word in ['supplier', 'delivery', 'shipment']):
            return 'supplier'
        elif any(word in subject_lower or word in body_lower for word in ['logistics', 'unloading', 'transport']):
            return 'logistics'
        else:
            return 'other'
    
    def process_emails_with_ai(self) -> Dict[str, Any]:
        """Process emails using Gemini AI based on category"""
        try:
            with open(self.inbox_path, 'r') as f:
                emails = json.load(f)
            
            processed_count = 0
            
            for email in emails:
                category = email.get('category', 'other')
                
                if category == 'warehouse':
                    self.process_warehouse_email(email)
                elif category == 'supplier':
                    self.process_supplier_email(email)
                elif category == 'logistics':
                    self.process_logistics_email(email)
                
                processed_count += 1
            
            return {
                "success": True,
                "processed_count": processed_count
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def process_warehouse_email(self, email: Dict):
        """Process warehouse email and log discrepancies"""
        try:
            prompt = f"""
            Analyze this warehouse email and extract physical quantity information:
            
            Subject: {email['subject']}
            Body: {email['body']}
            
            Extract:
            1. Material code/ID mentioned
            2. Physical quantity reported
            3. Any discrepancies mentioned
            4. Condition of materials
            
            Return as JSON format.
            """
            
            response = self.model.generate_content(prompt)
            analysis = response.text
            
            # Try to extract JSON from response
            try:
                import json
                analysis_data = json.loads(analysis)
            except:
                analysis_data = {"raw_analysis": analysis}
            
            discrepancy_entry = {
                "email_subject": email['subject'],
                "sender": email['sender'],
                "received_time": email['received_time'],
                "analysis": analysis_data,
                "processed_at": datetime.now().isoformat()
            }
            
            # Load existing discrepancies
            try:
                with open(self.discrepancy_path, 'r') as f:
                    discrepancies = json.load(f)
            except:
                discrepancies = []
            
            discrepancies.append(discrepancy_entry)
            
            with open(self.discrepancy_path, 'w') as f:
                json.dump(discrepancies, f, indent=2)
                
        except Exception as e:
            print(f"Error processing warehouse email: {e}")
    
    def process_supplier_email(self, email: Dict):
        """Process supplier email and log information"""
        try:
            prompt = f"""
            Analyze this supplier email and extract delivery information:
            
            Subject: {email['subject']}
            Body: {email['body']}
            
            Extract:
            1. Material code/ID mentioned
            2. Delivery date mentioned
            3. Quantity to be delivered
            4. Any delays or issues
            5. Tracking information if available
            
            Return as JSON format.
            """
            
            response = self.model.generate_content(prompt)
            analysis = response.text
            
            try:
                analysis_data = json.loads(analysis)
            except:
                analysis_data = {"raw_analysis": analysis}
            
            supplier_entry = {
                "email_subject": email['subject'],
                "sender": email['sender'],
                "received_time": email['received_time'],
                "analysis": analysis_data,
                "processed_at": datetime.now().isoformat()
            }
            
            try:
                with open(self.supplier_response_path, 'r') as f:
                    responses = json.load(f)
            except:
                responses = []
            
            responses.append(supplier_entry)
            
            with open(self.supplier_response_path, 'w') as f:
                json.dump(responses, f, indent=2)
                
        except Exception as e:
            print(f"Error processing supplier email: {e}")
    
    def process_logistics_email(self, email: Dict):
        """Process logistics email and log unloading status"""
        try:
            prompt = f"""
            Analyze this logistics email for unloading information:
            
            Subject: {email['subject']}
            Body: {email['body']}
            
            Extract:
            1. Material code/ID mentioned
            2. Unloading status (complete/in-progress/pending)
            3. Quantity unloaded
            4. Expected completion time if not complete
            5. Any issues or delays
            
            Return as JSON format.
            """
            
            response = self.model.generate_content(prompt)
            analysis = response.text
            
            try:
                analysis_data = json.loads(analysis)
            except:
                analysis_data = {"raw_analysis": analysis}
            
            # Check if unloading is complete
            unloading_complete = False
            if isinstance(analysis_data, dict):
                status = analysis_data.get('unloading_status', '').lower()
                unloading_complete = 'complete' in status
            
            unloading_entry = {
                "email_subject": email['subject'],
                "sender": email['sender'],
                "received_time": email['received_time'],
                "analysis": analysis_data,
                "unloading_complete": unloading_complete,
                "processed_at": datetime.now().isoformat()
            }
            
            try:
                with open(self.unloading_status_path, 'r') as f:
                    statuses = json.load(f)
            except:
                statuses = []
            
            statuses.append(unloading_entry)
            
            with open(self.unloading_status_path, 'w') as f:
                json.dump(statuses, f, indent=2)
                
        except Exception as e:
            print(f"Error processing logistics email: {e}")
    
    def run_full_automation(self) -> Dict[str, Any]:
        """Run the complete automation sequence"""
        results = {
            "start_time": datetime.now().isoformat(),
            "steps": []
        }
        
        # Step 1: Detect critical materials
        print("Step 1: Detecting critical materials...")
        step1_result = self.detect_critical_materials()
        results["steps"].append({"step": 1, "name": "Detect Critical Materials", "result": step1_result})
        
        if not step1_result["success"]:
            return results
        
        # Step 2: Compose and send emails
        print("Step 2: Composing and sending emails...")
        step2_result = self.compose_and_send_emails()
        results["steps"].append({"step": 2, "name": "Send Emails", "result": step2_result})
        
        # Step 3: Fetch emails from Outlook
        print("Step 3: Fetching emails from Outlook...")
        step3_result = self.fetch_emails_from_outlook()
        results["steps"].append({"step": 3, "name": "Fetch Emails", "result": step3_result})
        
        # Step 4: Process emails with AI
        print("Step 4: Processing emails with AI...")
        step4_result = self.process_emails_with_ai()
        results["steps"].append({"step": 4, "name": "Process Emails with AI", "result": step4_result})
        
        results["end_time"] = datetime.now().isoformat()
        results["success"] = all(step["result"]["success"] for step in results["steps"])
        
        return results