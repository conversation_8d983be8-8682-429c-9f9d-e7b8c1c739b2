from fastapi import FastAPI, BackgroundTasks
import api_model
import os
from supply_chain_automation import SupplyChainAutomation
import json

HOST = "127.0.0.1"
PORT = 7777

app = FastAPI()
sc_automation = SupplyChainAutomation()

@app.get("/hello/{name}")
def read_root(name: str):
    return f"hello {name}"

@app.post("/open-explorer/")
def open_explorer(model: api_model.PathModel):
    
    os.startfile(model.path)

    return f"Opening {model.path}"

@app.post("/supply-chain/start-automation/")
def start_automation(background_tasks: BackgroundTasks):
    """Start the full supply chain automation process"""
    try:
        result = sc_automation.run_full_automation()
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.get("/supply-chain/critical-materials/")
def get_critical_materials():
    """Get critical materials"""
    try:
        result = sc_automation.detect_critical_materials()
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.get("/supply-chain/status/")
def get_automation_status():
    """Get current automation status"""
    try:
        status = {
            "critical_materials": os.path.exists(sc_automation.critical_materials_path),
            "sent_emails": os.path.exists(sc_automation.sent_path),
            "inbox_emails": os.path.exists(sc_automation.inbox_path),
            "discrepancies": os.path.exists(sc_automation.discrepancy_path),
            "supplier_responses": os.path.exists(sc_automation.supplier_response_path),
            "unloading_status": os.path.exists(sc_automation.unloading_status_path)
        }
        return {"success": True, "status": status}
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.get("/supply-chain/results/{file_type}")
def get_results(file_type: str):
    """Get results from specific JSON files"""
    try:
        file_paths = {
            "critical": sc_automation.critical_materials_path,
            "sent": sc_automation.sent_path,
            "inbox": sc_automation.inbox_path,
            "discrepancy": sc_automation.discrepancy_path,
            "supplier": sc_automation.supplier_response_path,
            "unloading": sc_automation.unloading_status_path
        }
        
        if file_type not in file_paths:
            return {"success": False, "error": "Invalid file type"}
        
        file_path = file_paths[file_type]
        if not os.path.exists(file_path):
            return {"success": False, "error": "File not found"}
        
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        return {"success": True, "data": data}
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    import asyncio
    import uvicorn

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    loop.run_until_complete(uvicorn.run(app, host=HOST, port=PORT))