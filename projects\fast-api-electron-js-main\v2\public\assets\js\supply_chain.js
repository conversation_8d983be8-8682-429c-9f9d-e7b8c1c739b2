// Supply Chain Automation JavaScript

const API_BASE_URL = 'http://127.0.0.1:7777';
let automationInProgress = false;

// Utility functions
function addLog(message, type = 'info') {
    const logsContainer = document.getElementById('systemLogs');
    const timestamp = new Date().toLocaleTimeString();
    const logClass = `log-${type}`;
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${logClass}`;
    logEntry.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    
    logsContainer.appendChild(logEntry);
    logsContainer.scrollTop = logsContainer.scrollHeight;
}

function updateStepStatus(stepNumber, status, details = '') {
    const statusElement = document.getElementById(`step${stepNumber}Status`);
    const detailsElement = document.getElementById(`step${stepNumber}Details`);
    
    // Remove all status classes
    statusElement.classList.remove('status-pending', 'status-running', 'status-completed', 'status-error');
    
    // Add new status class
    statusElement.classList.add(`status-${status}`);
    
    // Update details
    if (detailsElement && details) {
        detailsElement.textContent = details;
    }
}

function updateProgress(percentage) {
    const progressBar = document.getElementById('overallProgress');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = `${percentage}%`;
    progressText.textContent = `${percentage}%`;
}

// API functions
async function makeAPICall(endpoint, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
        const result = await response.json();
        
        return result;
    } catch (error) {
        addLog(`API Error: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

// Main automation function
async function startAutomation() {
    if (automationInProgress) {
        addLog('Automation already in progress', 'warning');
        return;
    }
    
    automationInProgress = true;
    const startButton = document.getElementById('startAutomation');
    startButton.disabled = true;
    startButton.innerHTML = `
        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
        Running...
    `;
    
    addLog('Starting autonomous supply chain automation...', 'info');
    updateProgress(0);
    
    try {
        // Reset all steps to pending
        for (let i = 1; i <= 4; i++) {
            updateStepStatus(i, 'pending');
        }
        
        // Step 1: Detect Critical Materials
        addLog('Step 1: Detecting critical materials...', 'info');
        updateStepStatus(1, 'running');
        updateProgress(10);
        
        const step1Result = await makeAPICall('/supply-chain/critical-materials/');
        
        if (step1Result.success) {
            updateStepStatus(1, 'completed', `Found ${step1Result.count} critical materials`);
            addLog(`Step 1 completed: ${step1Result.count} critical materials detected`, 'info');
            updateProgress(25);
        } else {
            updateStepStatus(1, 'error', step1Result.error);
            addLog(`Step 1 failed: ${step1Result.error}`, 'error');
            throw new Error(step1Result.error);
        }
        
        // Step 2: Start full automation (this will handle all remaining steps)
        addLog('Starting full automation process...', 'info');
        updateStepStatus(2, 'running');
        updateProgress(30);
        
        const automationResult = await makeAPICall('/supply-chain/start-automation/', 'POST');
        
        if (automationResult.success) {
            // Process each step result
            automationResult.steps.forEach((step, index) => {
                const stepNum = step.step;
                if (step.result.success) {
                    updateStepStatus(stepNum, 'completed', getStepDetails(step));
                    addLog(`Step ${stepNum} completed: ${step.name}`, 'info');
                } else {
                    updateStepStatus(stepNum, 'error', step.result.error);
                    addLog(`Step ${stepNum} failed: ${step.result.error}`, 'error');
                }
            });
            
            updateProgress(100);
            addLog('Automation completed successfully!', 'info');
            
        } else {
            addLog(`Automation failed: ${automationResult.error}`, 'error');
            updateProgress(0);
        }
        
    } catch (error) {
        addLog(`Automation error: ${error.message}`, 'error');
        updateProgress(0);
    } finally {
        automationInProgress = false;
        startButton.disabled = false;
        startButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
                <polygon points="5,3 19,12 5,21 5,3"></polygon>
            </svg>
            Start AI Automation
        `;
    }
}

function getStepDetails(step) {
    switch (step.step) {
        case 1:
            return `Detected ${step.result.count || 0} critical materials`;
        case 2:
            return `Sent ${step.result.sent_count || 0} emails`;
        case 3:
            return `Fetched ${step.result.count || 0} emails`;
        case 4:
            return `Processed ${step.result.processed_count || 0} emails`;
        default:
            return 'Completed';
    }
}

// Status refresh function
async function refreshStatus() {
    addLog('Refreshing system status...', 'info');
    
    try {
        const statusResult = await makeAPICall('/supply-chain/status/');
        
        if (statusResult.success) {
            const status = statusResult.status;
            
            // Update step statuses based on file existence
            updateStepStatus(1, status.critical_materials ? 'completed' : 'pending');
            updateStepStatus(2, status.sent_emails ? 'completed' : 'pending');
            updateStepStatus(3, status.inbox_emails ? 'completed' : 'pending');
            updateStepStatus(4, (status.discrepancies || status.supplier_responses || status.unloading_status) ? 'completed' : 'pending');
            
            addLog('Status refreshed successfully', 'info');
        } else {
            addLog(`Failed to refresh status: ${statusResult.error}`, 'error');
        }
    } catch (error) {
        addLog(`Status refresh error: ${error.message}`, 'error');
    }
}

// Results viewing function
async function viewResults() {
    addLog('Loading results...', 'info');
    
    try {
        const resultTypes = ['critical', 'sent', 'inbox', 'discrepancy', 'supplier', 'unloading'];
        const results = {};
        
        for (const type of resultTypes) {
            const result = await makeAPICall(`/supply-chain/results/${type}`);
            if (result.success) {
                results[type] = result.data;
            }
        }
        
        displayResults(results);
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('resultsModal'));
        modal.show();
        
    } catch (error) {
        addLog(`Error loading results: ${error.message}`, 'error');
    }
}

function displayResults(results) {
    const resultsContent = document.getElementById('resultsContent');
    let html = '';
    
    const resultTitles = {
        critical: 'Critical Materials',
        sent: 'Sent Emails',
        inbox: 'Inbox Emails',
        discrepancy: 'Warehouse Discrepancies',
        supplier: 'Supplier Responses',
        unloading: 'Unloading Status'
    };
    
    for (const [type, data] of Object.entries(results)) {
        if (data && data.length > 0) {
            html += `
                <div class="mb-4">
                    <h6 class="card-title">${resultTitles[type]}</h6>
                    <div class="bg-dark p-3 rounded">
                        <pre class="text-light mb-0" style="font-size: 0.8rem; max-height: 200px; overflow-y: auto;">
${JSON.stringify(data, null, 2)}
                        </pre>
                    </div>
                </div>
            `;
        }
    }
    
    if (html === '') {
        html = '<p class="text-center card-text">No results available yet. Run the automation first.</p>';
    }
    
    resultsContent.innerHTML = html;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    addLog('Supply Chain Automation System loaded', 'info');
    refreshStatus();
    
    // Auto-refresh status every 30 seconds
    setInterval(refreshStatus, 30000);
});

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    if (event.ctrlKey && event.key === 'Enter') {
        startAutomation();
    } else if (event.key === 'F5') {
        event.preventDefault();
        refreshStatus();
    }
});

// Export functions for global access
window.startAutomation = startAutomation;
window.refreshStatus = refreshStatus;
window.viewResults = viewResults;