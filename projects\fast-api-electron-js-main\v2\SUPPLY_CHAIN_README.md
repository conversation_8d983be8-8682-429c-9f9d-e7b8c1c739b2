# SC Copilot KI - Autonomous Supply Chain System

## Overview

This is an autonomous supply chain management system that integrates with your existing FastAPI + Electron application. The system automatically:

1. **Detects Critical Materials** - Analyzes inventory data to identify materials with low stock (red: <100, yellow: =100)
2. **Sends Automated Emails** - Composes and sends targeted emails to warehouse, supplier, and logistics teams
3. **Processes Email Responses** - Uses Gemini AI to analyze incoming emails and extract relevant information
4. **Generates Reports** - Creates JSON reports for discrepancies, supplier responses, and unloading status

## Features

- **Glassmorphism UI** - Modern, translucent interface with blur effects
- **Real-time Status Updates** - Live progress tracking of automation steps
- **AI-Powered Email Processing** - Gemini AI analyzes email content and extracts structured data
- **Outlook Integration** - Direct integration with Microsoft Outlook using pywin32
- **Comprehensive Logging** - Detailed system logs with color-coded messages

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment Variables

1. Copy `.env.example` to `.env`
2. Add your Gemini AI API key:
   ```
   GEMINI_API_KEY=your-actual-gemini-api-key-here
   ```

### 3. Prepare Data Files

Ensure your data files are in the correct format:

- `data/data.csv` - Inventory data with columns: Index, Material, Material short text, Stock
- `data/email_info.csv` - Email addresses with columns: type, email_address

### 4. Configure Outlook

Make sure Microsoft Outlook is installed and configured on your system. The application uses pywin32 to interact with Outlook.

### 5. Run the Application

1. Start the FastAPI server:
   ```bash
   python engine/api.py
   ```

2. Open the Electron application and click "Launch Supply Chain AI"

## File Structure

```
├── engine/
│   ├── api.py                          # Main FastAPI application
│   ├── api_model.py                    # Pydantic models
│   └── supply_chain_automation.py     # Core automation logic
├── data/
│   ├── data.csv                        # Inventory data
│   ├── email_info.csv                  # Email configuration
│   ├── critical_materials.json         # Generated: Critical materials
│   ├── inbox.json                      # Generated: Fetched emails
│   ├── sent.json                       # Generated: Sent emails
│   ├── discrepancy.json                # Generated: Warehouse discrepancies
│   ├── supplier_response.json          # Generated: Supplier responses
│   └── unloading_status.json           # Generated: Unloading status
├── public/
│   ├── src/
│   │   └── supply_chain.html           # Supply chain interface
│   └── assets/
│       └── js/
│           └── supply_chain.js         # Frontend JavaScript
```

## API Endpoints

- `POST /supply-chain/start-automation/` - Start full automation process
- `GET /supply-chain/critical-materials/` - Get critical materials
- `GET /supply-chain/status/` - Get automation status
- `GET /supply-chain/results/{file_type}` - Get specific result files

## Usage

1. **Launch the System**: Click "Launch Supply Chain AI" from the main interface
2. **Start Automation**: Click "Start AI Automation" to begin the process
3. **Monitor Progress**: Watch real-time status updates in the operations panel
4. **View Results**: Click "View Results" to see generated reports
5. **Refresh Status**: Use "Refresh Status" to update the current state

## Automation Steps

### Step 1: Detect Critical Materials
- Reads `data/data.csv`
- Identifies materials with stock < 100 (red) or stock = 100 (yellow)
- Saves results to `critical_materials.json`

### Step 2: Send Emails
- Composes targeted emails for each critical material
- Sends to warehouse (inventory check), supplier (delivery updates), logistics (unloading status)
- Saves sent email log to `sent.json`

### Step 3: Fetch Emails
- Retrieves recent emails from Outlook inbox
- Categorizes emails by content (warehouse/supplier/logistics)
- Saves to `inbox.json`

### Step 4: Process with AI
- Uses Gemini AI to analyze email content
- Extracts structured information based on email category
- Saves results to respective JSON files

## Troubleshooting

### Common Issues

1. **Outlook Connection Failed**
   - Ensure Outlook is installed and running
   - Check if pywin32 is properly installed
   - Try running as administrator

2. **Gemini AI Errors**
   - Verify your API key is correct
   - Check internet connection
   - Ensure you have sufficient API quota

3. **File Permission Errors**
   - Ensure the application has write permissions to the data folder
   - Check if files are not locked by other applications

### Logs

System logs are displayed in real-time in the interface. Look for:
- **INFO** (blue): Normal operations
- **WARNING** (yellow): Non-critical issues
- **ERROR** (red): Critical problems

## Customization

### Email Templates
Modify the email templates in `supply_chain_automation.py`:
- `send_warehouse_email()`
- `send_supplier_email()`
- `send_logistics_email()`

### AI Prompts
Customize AI analysis prompts in:
- `process_warehouse_email()`
- `process_supplier_email()`
- `process_logistics_email()`

### UI Styling
Modify the glassmorphism effects in `supply_chain.html` CSS section.

## Security Notes

- Store sensitive API keys in environment variables
- Ensure email credentials are properly secured
- Consider implementing authentication for production use
- Regularly update dependencies for security patches

## Support

For issues or questions:
1. Check the system logs for error details
2. Verify all dependencies are installed correctly
3. Ensure data files are in the correct format
4. Check API key and email configuration